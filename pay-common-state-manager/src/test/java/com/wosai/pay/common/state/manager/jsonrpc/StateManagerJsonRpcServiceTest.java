package com.wosai.pay.common.state.manager.jsonrpc;

import com.wosai.pay.common.state.manager.ProviderMchIdEntity;
import com.wosai.pay.common.state.manager.ProviderMchIdProcessor;
import com.wosai.pay.common.state.manager.ProviderMchIdStateResult;
import com.wosai.pay.common.state.manager.TestConfiguration;
import com.wosai.pay.common.state.manager.jsonrpc.dto.JsonRpcResponse;
import com.wosai.pay.common.state.manager.jsonrpc.dto.StateQueryRequest;
import com.wosai.pay.common.state.manager.jsonrpc.dto.StateUpdateRequest;
import com.wosai.pay.common.state.manager.registry.StateManagerProcessorRegistry;
import com.wosai.pay.common.state.manager.service.StateManagerService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * 状态管理 JSON-RPC 服务测试
 * 
 * <AUTHOR>
 * @since 1.2.7-SNAPSHOT
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfiguration.class)
@Slf4j
public class StateManagerJsonRpcServiceTest {

    @Resource
    private StateManagerService stateManagerService;

    private StateManagerJsonRpcServiceImpl jsonRpcService;
    private ProviderMchIdProcessor providerMchIdProcessor;

    @Before
    public void setUp() {
        // 创建并注册ProviderMchIdEntity的处理器
        providerMchIdProcessor = new ProviderMchIdProcessor();
        StateManagerProcessorRegistry.clear();
        StateManagerProcessorRegistry.register(providerMchIdProcessor);

        // 创建 JSON-RPC 服务实例
        jsonRpcService = new StateManagerJsonRpcServiceImpl();
        // 通过反射设置 stateManagerService 字段
        try {
            java.lang.reflect.Field field = StateManagerJsonRpcServiceImpl.class.getDeclaredField("stateManagerService");
            field.setAccessible(true);
            field.set(jsonRpcService, stateManagerService);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject stateManagerService", e);
        }
    }

    @Test
    public void testQueryState_Success() {
        // 准备测试数据
        StateQueryRequest request = new StateQueryRequest();
        request.setEntityType("ProviderMchIdEntity");
        request.setBusiness("settlement");
        request.setTraceId("test-trace-001");

        Map<String, Object> entityMap = new HashMap<>();
        entityMap.put("provider", 1001);
        entityMap.put("providerMchId", "test_provider_mch_id_001");
        request.setEntity(entityMap);

        // 执行测试
        JsonRpcResponse<Object> response = jsonRpcService.queryState(request);

        // 验证结果
        assertNotNull("响应不应该为null", response);
        assertEquals("响应状态码应该为200", Integer.valueOf(200), response.getCode());
        assertEquals("响应消息应该为Success", "Success", response.getMessage());
        assertEquals("追踪ID应该匹配", "test-trace-001", response.getTraceId());
        assertNotNull("响应数据不应该为null", response.getData());
        assertNotNull("响应时间戳不应该为null", response.getTimestamp());

        // 验证返回的状态结果
        assertTrue("返回数据应该是StateManagerResult类型", response.getData() instanceof ProviderMchIdStateResult);
        ProviderMchIdStateResult result = (ProviderMchIdStateResult) response.getData();
        assertEquals("Provider应该匹配", Integer.valueOf(1001), result.getProvider());
        assertEquals("ProviderMchId应该匹配", "test_provider_mch_id_001", result.getProviderMchId());
        assertNotNull("状态值不应该为null", result.getState());
        assertNotNull("子状态列表不应该为null", result.getSubStateList());

        log.info("Query state test passed: {}", response);
    }

    @Test
    public void testQueryState_MissingParameters() {
        // 准备缺少参数的测试数据
        StateQueryRequest request = new StateQueryRequest();
        request.setEntityType("ProviderMchIdEntity");
        // 缺少 business 参数

        // 执行测试
        JsonRpcResponse<Object> response = jsonRpcService.queryState(request);

        // 验证结果
        assertNotNull("响应不应该为null", response);
        assertEquals("响应状态码应该为400", Integer.valueOf(400), response.getCode());
        assertTrue("响应消息应该包含参数错误信息", response.getMessage().contains("Missing required parameters"));
        assertNull("响应数据应该为null", response.getData());

        log.info("Query state missing parameters test passed: {}", response);
    }

    @Test
    public void testChangeState_Success() {
        // 准备测试数据
        StateUpdateRequest request = new StateUpdateRequest();
        request.setEntityType("ProviderMchIdEntity");
        request.setBusiness("settlement");
        request.setSubStateType(1);
        request.setEnabled(true);
        request.setRemark("Test state change");
        request.setTraceId("test-trace-002");

        Map<String, Object> entityMap = new HashMap<>();
        entityMap.put("provider", 1001);
        entityMap.put("providerMchId", "test_provider_mch_id_002");
        request.setEntity(entityMap);

        // 设置操作日志
        StateUpdateRequest.OperationLogDto operationLog = new StateUpdateRequest.OperationLogDto();
        operationLog.setOperatorUserId("test-user-001");
        operationLog.setOperatorUserName("Test User");
        operationLog.setOperationId("test-operation-001");
        operationLog.setDescription("Test state change operation");
        request.setOperationLog(operationLog);

        // 执行测试
        JsonRpcResponse<Boolean> response = jsonRpcService.changeState(request);

        // 验证结果
        assertNotNull("响应不应该为null", response);
        assertEquals("响应状态码应该为200", Integer.valueOf(200), response.getCode());
        assertEquals("响应消息应该为Success", "Success", response.getMessage());
        assertEquals("追踪ID应该匹配", "test-trace-002", response.getTraceId());
        assertNotNull("响应数据不应该为null", response.getData());
        assertTrue("状态修改应该成功", response.getData());

        log.info("Change state test passed: {}", response);
    }

    @Test
    public void testChangeState_MissingParameters() {
        // 准备缺少参数的测试数据
        StateUpdateRequest request = new StateUpdateRequest();
        request.setEntityType("ProviderMchIdEntity");
        request.setBusiness("settlement");
        // 缺少 subStateType 和 enabled 参数

        // 执行测试
        JsonRpcResponse<Boolean> response = jsonRpcService.changeState(request);

        // 验证结果
        assertNotNull("响应不应该为null", response);
        assertEquals("响应状态码应该为400", Integer.valueOf(400), response.getCode());
        assertTrue("响应消息应该包含参数错误信息", response.getMessage().contains("Missing required parameters"));
        assertNull("响应数据应该为null", response.getData());

        log.info("Change state missing parameters test passed: {}", response);
    }

    @Test
    public void testBatchQueryState_Success() {
        // 准备批量查询测试数据
        StateQueryRequest[] requests = new StateQueryRequest[2];

        // 第一个查询请求
        requests[0] = new StateQueryRequest();
        requests[0].setEntityType("ProviderMchIdEntity");
        requests[0].setBusiness("settlement");
        Map<String, Object> entityMap1 = new HashMap<>();
        entityMap1.put("provider", 1001);
        entityMap1.put("providerMchId", "test_provider_mch_id_batch_001");
        requests[0].setEntity(entityMap1);

        // 第二个查询请求
        requests[1] = new StateQueryRequest();
        requests[1].setEntityType("ProviderMchIdEntity");
        requests[1].setBusiness("settlement");
        Map<String, Object> entityMap2 = new HashMap<>();
        entityMap2.put("provider", 1002);
        entityMap2.put("providerMchId", "test_provider_mch_id_batch_002");
        requests[1].setEntity(entityMap2);

        // 执行测试
        JsonRpcResponse<Object> response = jsonRpcService.batchQueryState(requests);

        // 验证结果
        assertNotNull("响应不应该为null", response);
        assertEquals("响应状态码应该为200", Integer.valueOf(200), response.getCode());
        assertEquals("响应消息应该为Success", "Success", response.getMessage());
        assertNotNull("响应数据不应该为null", response.getData());
        assertNotNull("追踪ID不应该为null", response.getTraceId());

        log.info("Batch query state test passed: {}", response);
    }

    @Test
    public void testBatchChangeState_Success() {
        // 准备批量修改测试数据
        StateUpdateRequest[] requests = new StateUpdateRequest[2];

        // 第一个修改请求
        requests[0] = new StateUpdateRequest();
        requests[0].setEntityType("ProviderMchIdEntity");
        requests[0].setBusiness("settlement");
        requests[0].setSubStateType(1);
        requests[0].setEnabled(true);
        Map<String, Object> entityMap1 = new HashMap<>();
        entityMap1.put("provider", 1001);
        entityMap1.put("providerMchId", "test_provider_mch_id_batch_change_001");
        requests[0].setEntity(entityMap1);

        // 第二个修改请求
        requests[1] = new StateUpdateRequest();
        requests[1].setEntityType("ProviderMchIdEntity");
        requests[1].setBusiness("settlement");
        requests[1].setSubStateType(2);
        requests[1].setEnabled(false);
        Map<String, Object> entityMap2 = new HashMap<>();
        entityMap2.put("provider", 1002);
        entityMap2.put("providerMchId", "test_provider_mch_id_batch_change_002");
        requests[1].setEntity(entityMap2);

        // 执行测试
        JsonRpcResponse<Boolean[]> response = jsonRpcService.batchChangeState(requests);

        // 验证结果
        assertNotNull("响应不应该为null", response);
        assertEquals("响应状态码应该为200", Integer.valueOf(200), response.getCode());
        assertEquals("响应消息应该为Success", "Success", response.getMessage());
        assertNotNull("响应数据不应该为null", response.getData());
        assertNotNull("追踪ID不应该为null", response.getTraceId());

        log.info("Batch change state test passed: {}", response);
    }

    @Test
    public void testInvalidEntityType() {
        // 准备无效实体类型的测试数据
        StateQueryRequest request = new StateQueryRequest();
        request.setEntityType("InvalidEntityType");
        request.setBusiness("settlement");

        Map<String, Object> entityMap = new HashMap<>();
        entityMap.put("someField", "someValue");
        request.setEntity(entityMap);

        // 执行测试
        JsonRpcResponse<Object> response = jsonRpcService.queryState(request);

        // 验证结果
        assertNotNull("响应不应该为null", response);
        assertEquals("响应状态码应该为400", Integer.valueOf(400), response.getCode());
        assertTrue("响应消息应该包含实体类型错误信息", response.getMessage().contains("Unknown entity type"));

        log.info("Invalid entity type test passed: {}", response);
    }
}
