package com.wosai.pay.common.state.manager.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcServiceExporter;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceExporter;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * JSON-RPC 配置测试
 *
 * <AUTHOR>
 * @since 1.2.7-SNAPSHOT
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {StateManagerJsonRpcConfig.class, StateManagerJsonRpcServiceImpl.class})
@TestPropertySource(properties = {
    "state.manager.jsonrpc.enabled=true"
})
public class StateManagerJsonRpcConfigTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    public void testAutoJsonRpcServiceExporterBean() {
        // 验证 AutoJsonRpcServiceExporter Bean 是否存在
        assertTrue("AutoJsonRpcServiceExporter bean should exist", 
                   applicationContext.containsBean("autoJsonRpcServiceExporter"));
        
        AutoJsonRpcServiceExporter exporter = applicationContext.getBean("autoJsonRpcServiceExporter", AutoJsonRpcServiceExporter.class);
        assertNotNull("AutoJsonRpcServiceExporter should not be null", exporter);
    }

    @Test
    public void testJsonRpcServletBean() {
        // 验证 JSON-RPC Servlet Bean 是否存在
        assertTrue("JSON-RPC servlet bean should exist", 
                   applicationContext.containsBean("jsonRpcServlet"));
        
        @SuppressWarnings("unchecked")
        ServletRegistrationBean<JsonRpcServiceExporter> servletBean = 
            (ServletRegistrationBean<JsonRpcServiceExporter>) applicationContext.getBean("jsonRpcServlet");
        
        assertNotNull("ServletRegistrationBean should not be null", servletBean);
        assertEquals("Servlet name should be correct", "stateManagerJsonRpcServlet", servletBean.getServletName());
        assertTrue("URL mappings should contain /jsonrpc/stateManager", 
                   servletBean.getUrlMappings().contains("/jsonrpc/stateManager"));
    }

    @Test
    public void testStateManagerJsonRpcServiceBean() {
        // 验证 StateManagerJsonRpcService 实现类是否存在
        assertTrue("StateManagerJsonRpcService implementation should exist", 
                   applicationContext.containsBean("stateManagerJsonRpcServiceImpl"));
        
        StateManagerJsonRpcService service = applicationContext.getBean(StateManagerJsonRpcService.class);
        assertNotNull("StateManagerJsonRpcService should not be null", service);
        assertTrue("Service should be instance of StateManagerJsonRpcServiceImpl", 
                   service instanceof StateManagerJsonRpcServiceImpl);
    }
}
