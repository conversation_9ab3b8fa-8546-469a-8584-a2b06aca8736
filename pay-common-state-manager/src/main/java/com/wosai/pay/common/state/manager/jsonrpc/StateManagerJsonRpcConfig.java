package com.wosai.pay.common.state.manager.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcServiceExporter;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceExporter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 状态管理 JSON-RPC 配置类
 * 负责配置和暴露 JSON-RPC 服务接口
 *
 * <AUTHOR>
 * @since 1.2.7-SNAPSHOT
 */
@Configuration
@ConditionalOnProperty(
    prefix = "state.manager.jsonrpc", 
    name = "enabled", 
    havingValue = "true", 
    matchIfMissing = false
)
public class StateManagerJsonRpcConfig {

    /**
     * 自动导出 JSON-RPC 服务
     * 扫描所有带有 @AutoJsonRpcServiceImpl 注解的类并自动导出为 JSON-RPC 服务
     */
    @Bean
    public AutoJsonRpcServiceExporter autoJsonRpcServiceExporter() {
        AutoJsonRpcServiceExporter exporter = new AutoJsonRpcServiceExporter();
        // 设置基础包路径，扫描状态管理器的 JSON-RPC 服务
        exporter.setBasePackage("com.wosai.pay.common.state.manager.jsonrpc");
        return exporter;
    }

    /**
     * 注册 JSON-RPC Servlet
     * 将 JSON-RPC 服务映射到指定的 URL 路径
     */
    @Bean
    public ServletRegistrationBean<JsonRpcServiceExporter> jsonRpcServlet(
            StateManagerJsonRpcService stateManagerJsonRpcService) {
        
        // 创建 JSON-RPC 服务导出器
        JsonRpcServiceExporter exporter = new JsonRpcServiceExporter();
        exporter.setService(stateManagerJsonRpcService);
        exporter.setServiceInterface(StateManagerJsonRpcService.class);
        
        // 注册 Servlet
        ServletRegistrationBean<JsonRpcServiceExporter> registrationBean = 
            new ServletRegistrationBean<>(exporter);
        
        // 设置 URL 映射路径
        registrationBean.addUrlMappings("/jsonrpc/stateManager");
        registrationBean.setName("stateManagerJsonRpcServlet");
        
        return registrationBean;
    }
}
