package com.wosai.pay.common.state.manager.autoconfigure;

import com.wosai.pay.common.state.manager.dao.StateConfigDao;
import com.wosai.pay.common.state.manager.dao.StateDao;
import com.wosai.pay.common.state.manager.jsonrpc.StateManagerJsonRpcConfig;
import com.wosai.pay.common.state.manager.service.StateManagerConfig;
import com.wosai.pay.common.state.manager.service.StateManagerService;
import com.wosai.pay.common.state.manager.service.StateManagerServiceImpl;
import com.wosai.sp.business.logstash.service.BusinessOpLogService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.kafka.core.KafkaTemplate;

/**
 * 状态管理器自动配置类
 * 提供状态管理器相关 Bean 的自动配置
 *
 * <AUTHOR>
 * @since 1.2.7-SNAPSHOT
 */
@Configuration
@ConditionalOnClass({StateManagerService.class, NamedParameterJdbcTemplate.class})
@ConditionalOnProperty(prefix = "state.manager", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(StateManagerProperties.class)
@ComponentScan(basePackages = "com.wosai.pay.common.state.manager")
@Import(StateManagerJsonRpcConfig.class)
public class StateManagerAutoConfiguration {

    /**
     * 状态配置 DAO
     */
    @Bean
    @ConditionalOnMissingBean
    public StateConfigDao stateConfigDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        return new StateConfigDao(namedParameterJdbcTemplate);
    }

    /**
     * 状态 DAO
     */
    @Bean
    @ConditionalOnMissingBean
    public StateDao stateDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        return new StateDao("state", namedParameterJdbcTemplate);
    }

    /**
     * 状态管理配置
     */
    @Bean
    @ConditionalOnMissingBean
    public StateManagerConfig stateManagerConfig(StateConfigDao stateConfigDao, StateManagerProperties properties) {
        return new StateManagerConfig(stateConfigDao, properties.getConfigRefreshInterval(), properties.isConfigAutoRefresh());
    }

    /**
     * 状态管理服务
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnClass({KafkaTemplate.class, BusinessOpLogService.class})
    public StateManagerService stateManagerService(
            StateDao stateDao,
            KafkaTemplate<String, Object> kafkaTemplate,
            BusinessOpLogService businessOpLogService) {
        return new StateManagerServiceImpl(stateDao, kafkaTemplate, kafkaTemplate, businessOpLogService);
    }

    /**
     * 状态管理服务（无 Kafka 和业务日志服务的简化版本）
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(
        prefix = "state.manager", 
        name = "simple-mode", 
        havingValue = "true", 
        matchIfMissing = true
    )
    public StateManagerService simpleStateManagerService(StateDao stateDao) {
        return new StateManagerServiceImpl(stateDao, null, null, null);
    }
}
