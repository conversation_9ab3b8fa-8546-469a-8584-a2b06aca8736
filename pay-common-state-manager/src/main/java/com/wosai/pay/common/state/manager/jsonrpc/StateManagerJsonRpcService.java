package com.wosai.pay.common.state.manager.jsonrpc;

import com.googlecode.jsonrpc4j.JsonRpcMethod;
import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.pay.common.state.manager.jsonrpc.dto.StateQueryRequest;
import com.wosai.pay.common.state.manager.jsonrpc.dto.StateUpdateRequest;
import com.wosai.pay.common.state.manager.request.OperationLogRequest;
import com.wosai.pay.common.state.manager.result.StateManagerResult;

/**
 * 状态管理 JSON-RPC 服务接口
 * 提供统一的状态查询和修改接口
 *
 * <AUTHOR>
 * @since 1.2.7-SNAPSHOT
 */
@JsonRpcService("/stateManager")
public interface StateManagerJsonRpcService {

    /**
     * 查询状态
     *
     * @param request 状态查询请求
     * @return 查询结果
     */
    @JsonRpcMethod("queryState")
    StateManagerResult queryState(StateQueryRequest request);

    /**
     * 修改状态
     *
     * @param request 状态修改请求
     * @return 修改结果
     */
    @JsonRpcMethod("changeState")
    Boolean changeState(StateUpdateRequest request , OperationLogRequest operationLogRequest);

}
