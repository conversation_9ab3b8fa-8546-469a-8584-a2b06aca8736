package com.wosai.pay.common.state.manager.jsonrpc;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.pay.common.state.manager.exception.StateManageBizException;
import com.wosai.pay.common.state.manager.jsonrpc.dto.StateQueryRequest;
import com.wosai.pay.common.state.manager.jsonrpc.dto.StateUpdateRequest;
import com.wosai.pay.common.state.manager.request.AbstractEntity;
import com.wosai.pay.common.state.manager.request.OperationLogRequest;
import com.wosai.pay.common.state.manager.request.StateManagerRequest;
import com.wosai.pay.common.state.manager.result.StateManagerResult;
import com.wosai.pay.common.state.manager.service.StateManagerService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 状态管理 JSON-RPC 服务实现
 *
 * <AUTHOR>
 * @since 1.2.7-SNAPSHOT
 */
@AutoJsonRpcServiceImpl
public class StateManagerJsonRpcServiceImpl implements StateManagerJsonRpcService {

    private static final Logger logger = LoggerFactory.getLogger(StateManagerJsonRpcServiceImpl.class);

    @Resource
    private StateManagerService stateManagerService;

    private final ObjectMapper objectMapper;

    public StateManagerJsonRpcServiceImpl() {
        // 创建ObjectMapper并注册AbstractEntity的自定义反序列化器
        this.objectMapper = new ObjectMapper();

        // 创建自定义模块并注册AbstractEntity反序列化器
        SimpleModule module = new SimpleModule();
        module.addDeserializer(AbstractEntity.class, new AbstractEntity.AbstractStateObjectDeserializer());
        objectMapper.registerModule(module);

        // 配置ObjectMapper的其他属性
        objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Override
    public StateManagerResult queryState(StateQueryRequest request) {

        try {
            logger.info("JSON-RPC queryState request received,  business: {}", request.getBusiness());

            // 参数验证
            if (StringUtils.isBlank(request.getBusiness()) || request.getEntity() == null) {
                throw new StateManageBizException("Missing required parameters:  business, or entity");
            }

            // 转换为内部请求对象
            StateManagerRequest<AbstractEntity> stateManagerRequest = convertToStateManagerRequest(request);

            // 调用状态管理服务
            StateManagerResult result = stateManagerService.queryState(stateManagerRequest);

            logger.info("JSON-RPC queryState completed successfully, result: {}", result);

           return result;
        } catch (Exception e) {
            logger.error("JSON-RPC queryState internal error, ", e);
            throw new StateManageBizException("Internal server error: " + e.getMessage());
        }
    }

    @Override
    public Boolean changeState(StateUpdateRequest request,OperationLogRequest operationLogRequest) {

        try {
            logger.info("JSON-RPC changeState request received business: {}, subStateType: {}, enabled: {}", request.getBusiness(), request.getSubStateType(), request.getEnabled());

            // 参数验证
            if (StringUtils.isBlank(request.getBusiness()) || request.getEntity() == null || request.getSubStateType() == null || request.getEnabled() == null) {
                throw new StateManageBizException("Missing required parameters:  business, or entity, subStateType, or enabled");
            }


            // 转换为内部请求对象
            StateManagerRequest<AbstractEntity> stateManagerRequest = convertToStateManagerRequest(request);


            // 调用状态管理服务
            Boolean result = stateManagerService.changeState(stateManagerRequest, operationLogRequest);

            logger.info("JSON-RPC changeState completed successfully result: {}", result);


            return result;

        }  catch (Exception e) {
            logger.error("JSON-RPC changeState internal error", e);

            throw new StateManageBizException("Internal server error: " + e.getMessage());
        }
    }

    /**
     * 转换查询请求为内部请求对象
     */
    private StateManagerRequest<AbstractEntity> convertToStateManagerRequest(StateQueryRequest request) {
        StateManagerRequest<AbstractEntity> stateManagerRequest = new StateManagerRequest<>();
        stateManagerRequest.setBusiness(request.getBusiness());

        // 转换实体对象
        stateManagerRequest.setEntity(request.getEntity());

        return stateManagerRequest;
    }

    /**
     * 转换修改请求为内部请求对象
     */
    private StateManagerRequest<AbstractEntity> convertToStateManagerRequest(StateUpdateRequest request) {
        StateManagerRequest<AbstractEntity> stateManagerRequest = new StateManagerRequest<>();
        stateManagerRequest.setBusiness(request.getBusiness());
        stateManagerRequest.setSubStateType(request.getSubStateType());
        stateManagerRequest.setEnabled(request.getEnabled());
        stateManagerRequest.setRemark(request.getRemark());

        // 转换实体对象
        stateManagerRequest.setEntity(request.getEntity());

        return stateManagerRequest;
    }


}
