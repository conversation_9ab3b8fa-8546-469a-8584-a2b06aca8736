package com.wosai.pay.common.state.manager.autoconfigure;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 状态管理器配置属性
 *
 * <AUTHOR>
 * @since 1.2.7-SNAPSHOT
 */
@ConfigurationProperties(prefix = "state.manager")
public class StateManagerProperties {

    /**
     * 是否启用状态管理器
     */
    private boolean enabled = true;

    /**
     * 是否启用简单模式（不依赖 Kafka 和业务日志服务）
     */
    private boolean simpleMode = true;

    /**
     * 配置刷新间隔（秒）
     */
    private long configRefreshInterval = 300;

    /**
     * 是否启用配置自动刷新
     */
    private boolean configAutoRefresh = true;

    /**
     * JSON-RPC 相关配置
     */
    private JsonRpc jsonrpc = new JsonRpc();

    public static class JsonRpc {
        /**
         * 是否启用 JSON-RPC 服务
         */
        private boolean enabled = false;

        /**
         * JSON-RPC 服务 URL 映射
         */
        private String urlMapping = "/jsonrpc/*";

        /**
         * 内容类型
         */
        private String contentType = "application/json";

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getUrlMapping() {
            return urlMapping;
        }

        public void setUrlMapping(String urlMapping) {
            this.urlMapping = urlMapping;
        }

        public String getContentType() {
            return contentType;
        }

        public void setContentType(String contentType) {
            this.contentType = contentType;
        }
    }

    // Getters and Setters
    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isSimpleMode() {
        return simpleMode;
    }

    public void setSimpleMode(boolean simpleMode) {
        this.simpleMode = simpleMode;
    }

    public long getConfigRefreshInterval() {
        return configRefreshInterval;
    }

    public void setConfigRefreshInterval(long configRefreshInterval) {
        this.configRefreshInterval = configRefreshInterval;
    }

    public boolean isConfigAutoRefresh() {
        return configAutoRefresh;
    }

    public void setConfigAutoRefresh(boolean configAutoRefresh) {
        this.configAutoRefresh = configAutoRefresh;
    }

    public JsonRpc getJsonrpc() {
        return jsonrpc;
    }

    public void setJsonrpc(JsonRpc jsonrpc) {
        this.jsonrpc = jsonrpc;
    }
}
