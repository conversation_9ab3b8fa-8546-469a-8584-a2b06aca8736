package com.wosai.pay.common.state.manager.example;

import com.wosai.pay.common.state.manager.jsonrpc.StateManagerJsonRpcConfig;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * 状态管理器配置示例
 * 展示如何在您的应用中启用状态管理器的 JSON-RPC 服务
 *
 * <AUTHOR>
 * @since 1.2.7-SNAPSHOT
 */
@Configuration
@Import(StateManagerJsonRpcConfig.class)
public class StateManagerConfigurationExample {
    
    // 这个配置类展示了如何手动导入 JSON-RPC 配置
    // 如果您使用了自动配置，则不需要这个类
    
    /*
     * 在您的 application.yml 中添加以下配置来启用 JSON-RPC 服务：
     * 
     * state:
     *   manager:
     *     enabled: true
     *     jsonrpc:
     *       enabled: true
     *       url-mapping: /jsonrpc/*
     *       content-type: application/json
     */
}
